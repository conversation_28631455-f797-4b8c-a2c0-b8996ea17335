# == Schema Information
#
# Table name: users
#
#  id                             :bigint           not null, primary key
#  conversations_count            :integer          default(0), not null
#  email                          :string           not null
#  first_name                     :string
#  last_name                      :string
#  onboarding_completed           :boolean          default(FALSE)
#  onboarding_step                :string           default("personal")
#  password_digest                :string           not null
#  received_chat_requests_count   :integer          default(0), not null
#  scout_signup_completed         :boolean          default(FALSE)
#  sent_chat_requests_count       :integer          default(0), not null
#  signup_intent                  :string
#  talent_signup_completed        :boolean          default(FALSE)
#  time_zone                      :string
#  verification_email_sent_at     :datetime
#  verified                       :boolean          default(FALSE), not null
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  last_logged_in_organization_id :integer
#
# Indexes
#
#  index_users_on_email                           (email) UNIQUE
#  index_users_on_first_name                      (first_name)
#  index_users_on_first_name_and_last_name        (first_name,last_name)
#  index_users_on_last_logged_in_organization_id  (last_logged_in_organization_id)
#  index_users_on_last_name                       (last_name)
#  index_users_on_onboarding_completed            (onboarding_completed)
#  index_users_on_scout_signup_completed          (scout_signup_completed)
#  index_users_on_talent_signup_completed         (talent_signup_completed)
#  index_users_on_verified                        (verified)
#  index_users_on_verified_and_created_at         (verified,created_at)
#
class User < ApplicationRecord
  include AdminPermissions

  has_person_name
  has_secure_password
  pay_customer # Include the Pay::Billable concern and associations

  has_one :talent_profile, dependent: :destroy
  has_one_attached :avatar

  has_many :organization_memberships, dependent: :destroy
  has_many :organizations, through: :organization_memberships
  has_many :user_roles, dependent: :destroy
  has_many :roles, through: :user_roles
  has_many :saved_searches, dependent: :destroy
  has_many :conversation_participants
  has_many :conversations, through: :conversation_participants
  has_many :messages
  has_many :job_applications
  has_many :applied_jobs, through: :job_applications, source: :job
  has_many :job_invitations, dependent: :destroy
  has_many :saved_jobs, dependent: :destroy
  has_many :saved_jobs_list, through: :saved_jobs, source: :job
  has_many :talent_bookmarks, dependent: :destroy
  has_many :bookmarked_talents,
           -> { distinct },
           through: :talent_bookmarks,
           source: :talent_profile
  has_many :impersonation_logs, dependent: :destroy
  has_many :admin_audit_logs, foreign_key: 'admin_user_id', dependent: :destroy
  has_many :csv_exports, foreign_key: 'admin_user_id', dependent: :destroy
  has_many :security_alerts, dependent: :destroy
  has_many :talent_notes, dependent: :destroy

  # Badge associations
  has_many :badge_assignments, dependent: :destroy
  has_many :badge_types, through: :badge_assignments
  has_many :active_badge_assignments,
           -> { active },
           class_name: 'BadgeAssignment',
           dependent: :destroy
  has_many :active_badge_types,
           through: :active_badge_assignments,
           source: :badge_type

  # Badge analytics associations
  has_many :badge_views_received,
           class_name: 'BadgeView',
           foreign_key: 'viewed_user_id',
           dependent: :destroy
  has_many :badge_views_made,
           class_name: 'BadgeView',
           foreign_key: 'viewer_user_id',
           dependent: :destroy
  has_many :badge_clicks_received,
           class_name: 'BadgeClick',
           foreign_key: 'badge_owner_id',
           dependent: :destroy
  has_many :badge_clicks_made,
           class_name: 'BadgeClick',
           foreign_key: 'clicker_user_id',
           dependent: :destroy

  # Chat request associations
  has_many :sent_chat_requests,
           class_name: 'ChatRequest',
           foreign_key: 'scout_id',
           dependent: :destroy
  has_many :received_chat_requests,
           class_name: 'ChatRequest',
           foreign_key: 'talent_id',
           dependent: :destroy

  generates_token_for :email_verification, expires_in: 2.days do
    email
  end

  generates_token_for :password_reset, expires_in: 20.minutes do
    password_salt.last(10)
  end

  has_many :sessions, dependent: :destroy

  validates :email,
            presence: true,
            uniqueness: true,
            format: {
              with: URI::MailTo::EMAIL_REGEXP,
            }

  validates :password, allow_nil: true, length: { minimum: 12 }

  # validates :time_zone, inclusion: { in: ActiveSupport::TimeZone.all.map(&:name), message: "is not a valid time zone" }

  normalizes :email, with: -> { _1.strip.downcase }

  # Badge-related scopes
  scope :with_badge, ->(badge_type_or_name) {
    case badge_type_or_name
    when BadgeType
      joins(:active_badge_assignments)
        .where(badge_assignments: { badge_type: badge_type_or_name })
    when String, Symbol
      joins(:active_badge_assignments, :badge_types)
        .where(badge_types: { name: badge_type_or_name.to_s })
    else
      none
    end
  }

  scope :with_any_badge, ->(*badge_types_or_names) {
    return none if badge_types_or_names.empty?

    conditions = badge_types_or_names.map do |badge|
      case badge
      when BadgeType
        "badge_types.id = #{badge.id}"
      when String, Symbol
        "badge_types.name = '#{badge.to_s}'"
      else
        nil
      end
    end.compact

    return none if conditions.empty?

    joins(:active_badge_assignments, :badge_types)
      .where(conditions.join(' OR '))
      .distinct
  }

  scope :with_all_badges, ->(*badge_types_or_names) {
    return all if badge_types_or_names.empty?

    # Get badge type IDs for the query
    badge_type_ids = badge_types_or_names.map do |badge|
      case badge
      when BadgeType
        badge.id
      when String, Symbol
        BadgeType.find_by(name: badge.to_s)&.id
      else
        nil
      end
    end.compact

    return none if badge_type_ids.empty?

    # Find users who have active assignments for ALL specified badge types
    joins(:active_badge_assignments)
      .where(badge_assignments: { badge_type_id: badge_type_ids })
      .group('users.id')
      .having('COUNT(DISTINCT badge_assignments.badge_type_id) = ?', badge_type_ids.length)
  }

  scope :without_badge, ->(badge_type_or_name) {
    case badge_type_or_name
    when BadgeType
      where.not(
        id: joins(:active_badge_assignments)
              .where(badge_assignments: { badge_type: badge_type_or_name })
              .select(:id)
      )
    when String, Symbol
      where.not(
        id: joins(:active_badge_assignments, :badge_types)
              .where(badge_types: { name: badge_type_or_name.to_s })
              .select(:id)
      )
    else
      all
    end
  }

  scope :with_badge_count, ->(count) {
    joins(:active_badge_assignments)
      .group('users.id')
      .having('COUNT(badge_assignments.id) = ?', count)
  }

  scope :with_badge_count_greater_than, ->(count) {
    joins(:active_badge_assignments)
      .group('users.id')
      .having('COUNT(badge_assignments.id) > ?', count)
  }

  scope :with_badge_count_less_than, ->(count) {
    left_joins(:active_badge_assignments)
      .group('users.id')
      .having('COUNT(badge_assignments.id) < ?', count)
  }

  scope :with_badges_assigned_by, ->(admin_user) {
    joins(:badge_assignments)
      .where(badge_assignments: { admin: admin_user })
      .distinct
  }

  scope :with_recent_badge_assignments, ->(days = 30) {
    joins(:badge_assignments)
      .where('badge_assignments.assigned_at >= ?', days.days.ago)
      .distinct
  }

  before_validation if: :email_changed?, on: :update do
    self.verified = false
  end

  after_update if: :password_digest_previously_changed? do
    sessions.where.not(id: Current.session).delete_all
  end

  def has_role?(role_name)
    roles.exists?(name: role_name.to_s)
  end

  def superadmin?
    has_role?(:superadmin)
  end

  def full_name
    name.full
  end

  def avatar_color
    colors = [%w[bg-stone-100 text-stone-600]]

    # Use the user's ID or name to consistently get the same color
    index = (id || full_name.hash).abs % colors.length
    colors[index]
  end

  def initials
    if first_name.present? && last_name.present?
      "#{first_name.first}#{last_name.first}"
    elsif first_name.present?
      first_name.first
    elsif last_name.present?
      last_name.first
    else
      'XX' # Default if no name is present
    end
  end

  def pay_should_sync_customer?
    # super will invoke Pay's default (e-mail changed)
    # Check if first_name or last_name changed
    super || self.saved_change_to_first_name? || self.saved_change_to_last_name?
  end

  def stripe_attributes(pay_customer)
    {
      metadata: {
        pay_customer_id: pay_customer.id,
        user_id: id, # or pay_customer.owner_id
      },
    }
  end

  # Chat request helper methods
  def has_pending_chat_request_with?(talent_user)
    sent_chat_requests.pending.exists?(talent: talent_user)
  end

  # Badge-related instance methods
  def active_badges
    active_badge_types.order(:priority, :name)
  end

  def expired_badges
    badge_types.joins(:badge_assignments)
               .where(badge_assignments: { user: self })
               .where('badge_assignments.expires_at < ?', Time.current)
               .select('DISTINCT badge_types.*')
               .order(:priority, :name)
  end

  def permanent_badges
    badge_types.joins(:badge_assignments)
               .where(badge_assignments: { user: self, expires_at: nil })
               .select('DISTINCT badge_types.*')
               .order(:priority, :name)
  end

  def has_badge?(badge_type_or_name)
    case badge_type_or_name
    when BadgeType
      active_badge_assignments.joins(:badge_type).exists?(badge_types: { id: badge_type_or_name.id })
    when String, Symbol
      active_badge_assignments.joins(:badge_type).exists?(badge_types: { name: badge_type_or_name.to_s })
    else
      false
    end
  end

  def has_any_badge?(*badge_types_or_names)
    badge_types_or_names.any? { |badge| has_badge?(badge) }
  end

  def has_all_badges?(*badge_types_or_names)
    badge_types_or_names.all? { |badge| has_badge?(badge) }
  end

  def badge_assignment_for(badge_type_or_name)
    case badge_type_or_name
    when BadgeType
      badge_assignments.joins(:badge_type).find_by(badge_types: { id: badge_type_or_name.id })
    when String, Symbol
      badge_assignments.joins(:badge_type).find_by(badge_types: { name: badge_type_or_name.to_s })
    else
      nil
    end
  end

  def active_badge_assignment_for(badge_type_or_name)
    assignment = badge_assignment_for(badge_type_or_name)
    assignment&.active? ? assignment : nil
  end

  def badge_count
    active_badge_assignments.count
  end

  def total_badge_count
    badge_assignments.count
  end

  def badges_assigned_by(admin_user)
    badge_assignments.where(admin: admin_user)
  end

  def recent_badge_assignments(limit = 5)
    badge_assignments.includes(:badge_type, :admin)
                    .order(assigned_at: :desc)
                    .limit(limit)
  end

  # Badge analytics methods
  def badge_view_analytics(period: 30.days)
    views_in_period = badge_views_received.where('created_at > ?', period.ago)

    # Calculate unique viewers by combining authenticated and anonymous counts
    authenticated_viewers = views_in_period.where.not(viewer_user_id: nil).distinct.count(:viewer_user_id)
    anonymous_viewers = views_in_period.where(viewer_user_id: nil).distinct.count(:ip_address)

    {
      total_views: views_in_period.count,
      unique_viewers: authenticated_viewers + anonymous_viewers,
      anonymous_views: views_in_period.anonymous_views.count,
      authenticated_views: views_in_period.authenticated_views.count
    }
  end

  def badge_click_analytics(period: 30.days)
    clicks_in_period = badge_clicks_received.where('created_at > ?', period.ago)

    # Calculate unique clickers by combining authenticated and anonymous counts
    authenticated_clickers = clicks_in_period.where.not(clicker_user_id: nil).distinct.count(:clicker_user_id)
    anonymous_clickers = clicks_in_period.where(clicker_user_id: nil).distinct.count(:ip_address)

    {
      total_clicks: clicks_in_period.count,
      unique_clickers: authenticated_clickers + anonymous_clickers,
      clicks_by_context: clicks_in_period.group(:click_context).count,
      clicks_by_badge: clicks_in_period.joins(:badge_type)
                                      .group('badge_types.name')
                                      .count
    }
  end

  def badge_engagement_rate(period: 30.days)
    views = badge_views_received.where('created_at > ?', period.ago).count
    clicks = badge_clicks_received.where('created_at > ?', period.ago).count

    return 0 if views.zero?
    (clicks.to_f / views * 100).round(2)
  end

  def most_viewed_badges(period: 30.days, limit: 5)
    badge_views_received.where('created_at > ?', period.ago)
                       .joins("JOIN JSON_TABLE(badge_types_displayed, '$[*]' COLUMNS (badge_type_id INT PATH '$')) AS jt")
                       .joins("JOIN badge_types ON badge_types.id = jt.badge_type_id")
                       .group('badge_types.id', 'badge_types.name')
                       .order('COUNT(*) DESC')
                       .limit(limit)
                       .count
  end

  def most_clicked_badges(period: 30.days, limit: 5)
    badge_clicks_received.joins(:badge_type)
                        .where('badge_clicks.created_at > ?', period.ago)
                        .group('badge_types.id', 'badge_types.name')
                        .order('COUNT(*) DESC')
                        .limit(limit)
                        .count
  end
end
