# == Schema Information
#
# Table name: badge_types
#
#  id               :bigint           not null, primary key
#  active           :boolean          default(TRUE)
#  background_color :string           default("#ffffff"), not null
#  description      :text             not null
#  icon             :string           not null
#  name             :string           not null
#  priority         :integer          default(0)
#  text_color       :string           default("#000000"), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
# Indexes
#
#  index_badge_types_on_active               (active)
#  index_badge_types_on_active_and_priority  (active,priority)
#  index_badge_types_on_name                 (name) UNIQUE
#  index_badge_types_on_priority             (priority)
#
class BadgeType < ApplicationRecord
  # Associations
  has_many :badge_assignments, dependent: :restrict_with_error
  has_many :users, through: :badge_assignments

  # Analytics associations
  has_many :badge_clicks, dependent: :destroy

  # Validations
  validates :name, presence: true, uniqueness: true, length: { maximum: 50 }
  validates :description, presence: true, length: { maximum: 500 }
  validates :background_color,
            presence: true,
            format: {
              with: /\A#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})\z/,
              message: 'must be a valid hex color code (e.g., #ffffff or #fff)'
            }
  validates :text_color,
            presence: true,
            format: {
              with: /\A#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})\z/,
              message: 'must be a valid hex color code (e.g., #000000 or #000)'
            }
  validates :icon, presence: true, length: { maximum: 100 }
  validates :priority, presence: true, numericality: { greater_than_or_equal_to: 0 }

  # Custom validation to ensure color contrast (optional, can be disabled for flexibility)
  validate :sufficient_color_contrast, if: :validate_color_contrast?

  # Scopes
  scope :active, -> { where(active: true) }
  scope :inactive, -> { where(active: false) }
  scope :by_priority, -> { order(priority: :asc, name: :asc) }
  scope :by_name, -> { order(:name) }
  scope :recently_created, -> { order(created_at: :desc) }

  # Class methods
  def self.available_for_assignment
    active.by_priority
  end

  def self.with_assignment_counts
    left_joins(:badge_assignments)
      .group(:id)
      .select('badge_types.*, COUNT(badge_assignments.id) as assignments_count')
  end

  # Instance methods
  def can_be_deleted?
    badge_assignments.empty?
  end

  def assignment_count
    badge_assignments.count
  end

  def active_assignment_count
    badge_assignments.active.count
  end

  def assigned_users_count
    users.distinct.count
  end

  def display_name
    name
  end

  def css_styles
    {
      'background-color' => background_color,
      'color' => text_color
    }
  end

  # Convert 3-digit hex to 6-digit hex for consistency
  def normalized_background_color
    normalize_hex_color(background_color)
  end

  def normalized_text_color
    normalize_hex_color(text_color)
  end

  private

  def validate_color_contrast?
    # Allow disabling contrast validation for testing or special cases
    # Can be controlled via environment variable or application config
    ENV.fetch('VALIDATE_BADGE_COLOR_CONTRAST', 'true') == 'true'
  end

  def normalize_hex_color(color)
    return color unless color.match?(/\A#[A-Fa-f0-9]{3}\z/)

    # Convert #abc to #aabbcc
    "##{color[1] * 2}#{color[2] * 2}#{color[3] * 2}"
  end

  def sufficient_color_contrast
    return unless background_color.present? && text_color.present?

    begin
      bg_rgb = hex_to_rgb(normalized_background_color)
      text_rgb = hex_to_rgb(normalized_text_color)
      
      contrast_ratio = calculate_contrast_ratio(bg_rgb, text_rgb)
      
      # WCAG AA standard requires a contrast ratio of at least 4.5:1 for normal text
      if contrast_ratio < 4.5
        errors.add(:base, 'Text and background colors must have sufficient contrast for accessibility (minimum 4.5:1 ratio)')
      end
    rescue => e
      # If color parsing fails, let the format validation handle it
      Rails.logger.warn "Color contrast validation failed: #{e.message}"
    end
  end

  def hex_to_rgb(hex_color)
    hex = hex_color.gsub('#', '')
    [
      hex[0..1].to_i(16),
      hex[2..3].to_i(16),
      hex[4..5].to_i(16)
    ]
  end

  def calculate_contrast_ratio(rgb1, rgb2)
    l1 = relative_luminance(rgb1)
    l2 = relative_luminance(rgb2)
    
    lighter = [l1, l2].max
    darker = [l1, l2].min
    
    (lighter + 0.05) / (darker + 0.05)
  end

  def relative_luminance(rgb)
    r, g, b = rgb.map do |component|
      component = component / 255.0
      component <= 0.03928 ? component / 12.92 : ((component + 0.055) / 1.055) ** 2.4
    end

    0.2126 * r + 0.7152 * g + 0.0722 * b
  end

  # Badge analytics methods
  def click_analytics(period: 30.days)
    clicks_in_period = badge_clicks.where('created_at > ?', period.ago)

    # Calculate unique clickers by combining authenticated and anonymous counts
    authenticated_clickers = clicks_in_period.where.not(clicker_user_id: nil).distinct.count(:clicker_user_id)
    anonymous_clickers = clicks_in_period.where(clicker_user_id: nil).distinct.count(:ip_address)

    {
      total_clicks: clicks_in_period.count,
      unique_clickers: authenticated_clickers + anonymous_clickers,
      clicks_by_context: clicks_in_period.group(:click_context).count,
      clicks_by_owner: clicks_in_period.joins(:badge_owner)
                                      .group('users.email')
                                      .count
    }
  end

  def view_analytics(period: 30.days)
    # This requires a more complex query since badge views store badge_types_displayed as JSON
    views_with_this_badge = BadgeView.with_badge_type(id).where('created_at > ?', period.ago)

    # Calculate unique viewers by combining authenticated and anonymous counts
    authenticated_viewers = views_with_this_badge.where.not(viewer_user_id: nil).distinct.count(:viewer_user_id)
    anonymous_viewers = views_with_this_badge.where(viewer_user_id: nil).distinct.count(:ip_address)

    {
      total_views: views_with_this_badge.count,
      unique_viewers: authenticated_viewers + anonymous_viewers,
      anonymous_views: views_with_this_badge.anonymous_views.count,
      authenticated_views: views_with_this_badge.authenticated_views.count
    }
  end

  def engagement_rate(period: 30.days)
    views = view_analytics(period: period)[:total_views]
    clicks = click_analytics(period: period)[:total_clicks]

    return 0 if views.zero?
    (clicks.to_f / views * 100).round(2)
  end

  def top_clickers(period: 30.days, limit: 10)
    badge_clicks.joins(:clicker_user)
               .where('badge_clicks.created_at > ?', period.ago)
               .group('users.id', 'users.email')
               .order('COUNT(*) DESC')
               .limit(limit)
               .count
  end

  def performance_summary(period: 30.days)
    click_data = click_analytics(period: period)
    view_data = view_analytics(period: period)

    {
      badge_name: name,
      assignment_count: active_assignment_count,
      total_views: view_data[:total_views],
      total_clicks: click_data[:total_clicks],
      engagement_rate: engagement_rate(period: period),
      unique_viewers: view_data[:unique_viewers],
      unique_clickers: click_data[:unique_clickers],
      most_common_click_context: click_data[:clicks_by_context].max_by { |_, count| count }&.first
    }
  end
end
