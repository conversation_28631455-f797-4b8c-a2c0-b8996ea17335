import { Controller } from "@hotwired/stimulus";

// Badge Click Controller
// Handles click interactions for badges to open detailed modal
// Works in conjunction with badge_controller.js and badge_modal_controller.js
export default class extends Controller {
  static targets = ["badge"];

  static values = {
    badgeId: Number,
    badgeName: String,
    badgeDescription: String,
    badgeIcon: String,
    badgeBackgroundColor: String,
    badgeTextColor: String,
    badgeCriteria: String,
    modalSelector: { type: String, default: "#badge-modal" },
  };

  connect() {
    // Add clickable styling to badges
    this.addClickableStyles();

    // Set up click handlers
    this.setupClickHandlers();

    // Check for reduced motion preference
    this.prefersReducedMotion = window.matchMedia(
      "(prefers-reduced-motion: reduce)"
    ).matches;
  }

  disconnect() {
    // Clean up event listeners
    this.removeClickHandlers();
  }

  // Add visual indicators that badges are clickable
  addClickableStyles() {
    // If no targets are defined, apply styling to the controller element itself
    const badges =
      this.badgeTargets.length > 0 ? this.badgeTargets : [this.element];

    badges.forEach((badge) => {
      badge.classList.add("badge-clickable");
      badge.setAttribute("role", "button");
      badge.setAttribute("tabindex", "0");

      // Enhanced accessibility attributes
      const badgeName =
        this.badgeNameValue || this.extractBadgeName(badge) || "badge";
      badge.setAttribute("aria-label", `View details for ${badgeName} badge`);
      badge.setAttribute("aria-describedby", "badge-modal-description");
      badge.setAttribute("aria-haspopup", "dialog");
      badge.setAttribute("aria-expanded", "false");

      // Add keyboard interaction hint
      badge.title = `Press Enter or Space to view ${badgeName} badge details`;
    });
  }

  // Set up click and keyboard event handlers
  setupClickHandlers() {
    // If no targets are defined, apply handlers to the controller element itself
    const badges =
      this.badgeTargets.length > 0 ? this.badgeTargets : [this.element];

    badges.forEach((badge) => {
      badge.addEventListener("click", this.handleBadgeClick.bind(this));
      badge.addEventListener("keydown", this.handleBadgeKeydown.bind(this));
    });
  }

  // Remove event handlers
  removeClickHandlers() {
    // If no targets are defined, remove handlers from the controller element itself
    const badges =
      this.badgeTargets.length > 0 ? this.badgeTargets : [this.element];

    badges.forEach((badge) => {
      badge.removeEventListener("click", this.handleBadgeClick.bind(this));
      badge.removeEventListener("keydown", this.handleBadgeKeydown.bind(this));
    });
  }

  // Handle badge click events
  handleBadgeClick(event) {
    console.log("Badge clicked!", event.currentTarget);
    event.preventDefault();
    event.stopPropagation();

    // Add click animation if motion is not reduced
    if (!this.prefersReducedMotion) {
      this.animateClick(event.currentTarget);
    }

    // Open modal with badge data
    this.openBadgeModal(event.currentTarget);
  }

  // Handle keyboard navigation for badges
  handleBadgeKeydown(event) {
    if (event.key === "Enter" || event.key === " ") {
      event.preventDefault();
      this.handleBadgeClick(event);
    }
  }

  // Animate badge click for visual feedback
  animateClick(badgeElement) {
    // Add temporary click animation class
    badgeElement.classList.add("badge-clicking");

    // Remove animation class after animation completes
    setTimeout(() => {
      badgeElement.classList.remove("badge-clicking");
    }, 150);
  }

  // Open the badge modal with current badge data
  openBadgeModal(badgeElement) {
    console.log("Opening badge modal for:", badgeElement);

    // Find the modal element
    const modal = document.querySelector(this.modalSelectorValue);
    if (!modal) {
      console.error("Badge modal not found:", this.modalSelectorValue);
      return;
    }
    console.log("Found modal:", modal);

    // Get the modal controller
    const modalController =
      this.application.getControllerForElementAndIdentifier(
        modal,
        "badge-modal"
      );
    if (!modalController) {
      console.error("Badge modal controller not found");
      return;
    }
    console.log("Found modal controller:", modalController);

    // Update aria-expanded state for accessibility
    badgeElement.setAttribute("aria-expanded", "true");

    // Prepare badge data for modal
    const badgeData = this.prepareBadgeData(badgeElement);
    console.log("Badge data prepared:", badgeData);

    // Store reference to source badge for view transitions
    this.storeBadgeReference(badgeElement);

    // Open modal with badge data
    modalController.open(badgeData);

    // Listen for modal close to reset aria-expanded
    modal.addEventListener(
      "badge-modal:closed",
      () => {
        badgeElement.setAttribute("aria-expanded", "false");
      },
      { once: true }
    );

    // Dispatch custom event for analytics or other listeners
    this.dispatch("opened", {
      detail: {
        badgeId: this.badgeIdValue,
        badgeName: this.badgeNameValue,
        source: "click",
      },
    });
  }

  // Prepare badge data object for modal display
  prepareBadgeData(badgeElement) {
    // Extract data from controller values or element attributes
    const badgeData = {
      id: this.badgeIdValue || badgeElement.dataset.badgeId,
      name:
        this.badgeNameValue ||
        badgeElement.dataset.badgeName ||
        this.extractBadgeName(badgeElement),
      description:
        this.badgeDescriptionValue ||
        badgeElement.dataset.badgeDescription ||
        this.generateDefaultDescription(),
      icon:
        this.badgeIconValue ||
        badgeElement.dataset.badgeIcon ||
        this.extractBadgeIcon(badgeElement),
      backgroundColor:
        this.badgeBackgroundColorValue ||
        badgeElement.dataset.badgeBackgroundColor ||
        this.extractBackgroundColor(badgeElement),
      textColor:
        this.badgeTextColorValue ||
        badgeElement.dataset.badgeTextColor ||
        this.extractTextColor(badgeElement),
      criteria:
        this.badgeCriteriaValue ||
        badgeElement.dataset.badgeCriteria ||
        this.generateDefaultCriteria(),
    };

    return badgeData;
  }

  // Extract badge name from DOM element
  extractBadgeName(badgeElement) {
    const nameElement = badgeElement.querySelector(".badge-name");
    return nameElement ? nameElement.textContent.trim() : "Badge";
  }

  // Extract badge icon from DOM element
  extractBadgeIcon(badgeElement) {
    const iconElement = badgeElement.querySelector(".badge-icon");
    if (iconElement) {
      // Try to get icon name from data attribute or class
      return (
        iconElement.dataset.icon ||
        iconElement.dataset.phosphor ||
        this.extractIconFromClasses(iconElement) ||
        "star"
      );
    }
    return "star";
  }

  // Extract icon name from CSS classes (fallback method)
  extractIconFromClasses(iconElement) {
    const classes = Array.from(iconElement.classList);
    // Look for phosphor icon classes or other icon patterns
    const iconClass = classes.find(
      (cls) => cls.startsWith("ph-") || cls.includes("icon-")
    );
    if (iconClass) {
      return iconClass.replace("ph-", "").replace("icon-", "");
    }
    return null;
  }

  // Extract background color from element styles
  extractBackgroundColor(badgeElement) {
    const computedStyle = window.getComputedStyle(badgeElement);
    return computedStyle.backgroundColor || "#3B82F6";
  }

  // Extract text color from element styles
  extractTextColor(badgeElement) {
    const computedStyle = window.getComputedStyle(badgeElement);
    return computedStyle.color || "#FFFFFF";
  }

  // Generate default description if none provided
  generateDefaultDescription() {
    const badgeName = this.badgeNameValue || "This badge";
    return `${badgeName} represents a special achievement and recognition of exceptional performance on the platform.`;
  }

  // Extract badge name from element for accessibility
  extractBadgeName(badgeElement) {
    // Try various methods to extract badge name
    const nameFromText = badgeElement.querySelector(
      ".badge-name, .badge-title, [data-badge-name]"
    );
    if (nameFromText) {
      return nameFromText.textContent.trim() || nameFromText.dataset.badgeName;
    }

    // Try data attributes
    if (badgeElement.dataset.badgeName) {
      return badgeElement.dataset.badgeName;
    }

    // Try aria-label
    if (badgeElement.getAttribute("aria-label")) {
      return badgeElement
        .getAttribute("aria-label")
        .replace(/^View details for |badge$/gi, "")
        .trim();
    }

    // Fallback
    return "Achievement";
  }

  // Generate default criteria if none provided
  generateDefaultCriteria() {
    return "This badge is awarded to users who demonstrate exceptional skills, dedication, and positive contributions to the community.";
  }

  // Store reference to source badge for view transitions
  storeBadgeReference(badgeElement) {
    // Add view transition name for smooth animation
    if (CSS.supports("view-transition-name", "badge-source")) {
      badgeElement.style.viewTransitionName = "badge-source";

      // Remove the transition name after modal opens to avoid conflicts
      setTimeout(() => {
        badgeElement.style.viewTransitionName = "";
      }, 500);
    }
  }

  // Action method that can be called from templates
  openModal(event) {
    this.handleBadgeClick(event);
  }

  // Method to programmatically trigger modal opening
  triggerModal() {
    if (this.hasBadgeTarget) {
      this.openBadgeModal(this.badgeTarget);
    }
  }

  // Update badge data values (useful for dynamic content)
  updateBadgeData(newData) {
    Object.keys(newData).forEach((key) => {
      const valueName = `${key}Value`;
      if (this.hasOwnProperty(valueName)) {
        this[valueName] = newData[key];
      }
    });
  }
}
