<%# app/views/scout/talent/_talent_gallery_card.html.erb %>
<div class="relative flex flex-col items-center p-4 text-center transition-shadow duration-200 ease-in-out bg-white border rounded-lg shadow-md hover:shadow-lg">
  <%# Link to the full profile - making the whole card clickable %>
  <%= link_to "#", class: "absolute inset-0 z-10", data: { turbo_frame: "candidate-details-container", action: "click->candidate-details#load", candidate_details_url_param: scout_talent_path(talent_profile) } do %>
    <span class="sr-only">View profile for <%= talent_profile.user.name.full %></span>
  <% end %>

  <div class="relative z-0">
    <% if talent_profile.user.avatar.attached? %>
      <%= image_tag talent_profile.user.avatar, class: "w-24 h-24 mx-auto rounded-full object-cover border-2 border-stone-200" %>
    <% else %>
      <div class="flex items-center justify-center w-24 h-24 mx-auto text-3xl font-bold border-2 rounded-full text-stone-700 bg-stone-100 border-stone-200">
        <%= talent_profile.user.name.first[0].upcase %>
      </div>
    <% end %>

    <% if talent_profile.is_premium? %>
      <span class="absolute bottom-0 right-0 p-1 bg-yellow-400 rounded-full" title="Premium Talent">
        <%= phosphor_icon "crown-simple", style: :fill, class: "h-4 w-4 text-yellow-800" %>
      </span>
    <% end %>
  </div>

  <h3 class="w-full mt-3 text-lg font-semibold truncate text-stone-800" title="<%= talent_profile.user.name.full %>">
    <%= talent_profile.user.name.full %>
  </h3>

  <p class="w-full mt-1 text-sm truncate text-stone-600" title="<%= talent_profile.headline %>">
    <%= talent_profile.headline %>
  </p>

  <%# Badge display in gallery card %>
  <div class="mt-2">
    <%= render 'shared/user_badges',
        user: talent_profile.user,
        context: 'gallery',
        limit: 2,
        icon_only: true %>
  </div>

  <div class="mt-2">
    <span class="px-2 py-0.5 text-xs font-medium rounded-full <%= talent_profile.available? ? 'bg-green-100 text-green-700' : (talent_profile.limited? ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700') %>">
      <%= talent_profile.availability_status.humanize %>
    </span>
  </div>

  <%# Bookmark button - positioned absolutely for gallery card %>
  <div class="absolute z-20 top-2 right-2" id="bookmark_button_gallery_<%= talent_profile.id %>">
    <%= render partial: "scout/talent/bookmark_button", locals: { talent_profile: talent_profile, context: "gallery_#{talent_profile.id}" } %>
  </div>
</div>
