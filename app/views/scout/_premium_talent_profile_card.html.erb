<!-- Premium Talent Card with Side Actions -->
<div class="flex overflow-hidden bg-white border rounded-lg shadow-md border-stone-200">
  <!-- Left Action Sidebar -->
  <div class="flex flex-col items-center w-16 py-5 bg-indigo-900">
    <!-- User Initial -->
    <% if talent_profile.user.avatar.attached? %>
      <%= image_tag talent_profile.user.avatar, class: "w-10 h-10 mb-4 rounded-full object-cover" %>
    <% else %>
      <div class="flex items-center justify-center w-10 h-10 mb-4 text-xl font-bold text-indigo-900 bg-white rounded-full">
        <%= talent_profile.user.name.first[0].upcase %>
      </div>
    <% end %>
    <!-- Availability Indicator -->
    <div class="w-8 h-1 mb-4 <%= talent_profile.available? ? 'bg-green-500' : (talent_profile.limited? ? 'bg-yellow-500' : 'bg-red-500') %> rounded-full"></div>
    
    <!-- Sidebar Actions -->
    <div class="flex flex-col items-center gap-3 mt-auto">
      <!-- Bookmark Button -->
      <%= button_to bookmark_scout_talent_path(talent_profile), method: talent_profile.bookmarked_by?(Current.user) ? :delete : :post, class: "p-2 rounded-full #{Current.user&.bookmarked_talents&.include?(talent_profile) ? 'text-white' : 'text-stone-300 hover:text-white'}", form: { data: { turbo: true } } do %>
        <%= phosphor_icon "bookmark", class: "h-5 w-5" %>
      <% end %>
      
      <!-- View Detail Button -->
      <%= link_to scout_talent_path(talent_profile), class: "p-2 rounded-full text-stone-300 hover:text-white" do %>
        <%= phosphor_icon "eye", class: "h-5 w-5" %>
      <% end %>
      
      <!-- Message Button -->
      <% if Current.user.has_pending_chat_request_with?(talent_profile.user) %>
        <button disabled class="p-2 rounded-full text-stone-400 cursor-not-allowed">
          <%= phosphor_icon "check-circle", class: "h-5 w-5" %>
        </button>
      <% else %>
        <button type="button"
                data-action="click->chat-request-button#openModal"
                data-talent-user-id="<%= talent_profile.user.id %>"
                class="p-2 rounded-full text-stone-300 hover:text-white">
          <%= phosphor_icon "chat-circle", class: "h-5 w-5" %>
        </button>
      <% end %>
    </div>
  </div>
  
  <div class="flex-1 p-6">
    <!-- Header Section -->
    <div class="pb-4 mb-5 border-b border-stone-200">
      <div class="flex items-start justify-between">
        <div>
          <div class="flex items-center gap-2 mb-1">
            <span class="px-2 py-0.5 text-xs font-medium rounded-full <%= talent_profile.available? ? 'bg-green-100 text-green-700' : (talent_profile.limited? ? 'bg-yellow-100 text-yellow-700' : 'bg-red-100 text-red-700') %>">
              <%= talent_profile.availability_status.humanize %>
            </span>
            <span class="text-sm text-stone-500"><%= talent_profile.is_agency ? "Agency" : "Independent Ghostwriter" %></span>
          </div>
          <h3 class="text-xl font-bold text-stone-900"><%= talent_profile.user.name.full %></h3>
          <p class="font-medium text-indigo-600"><%= talent_profile.headline %></p>

          <%# Badge display in premium profile card %>
          <div class="mt-2">
            <%= render 'shared/user_badges',
                user: talent_profile.user,
                context: 'card',
                limit: 3 %>
          </div>
        </div>
        <div class="flex items-center gap-2">
          <div class="flex items-center gap-1 text-sm text-stone-500">
            <%= phosphor_icon "map-pin", class: "h-4 w-4" %>
            <span><%= talent_profile.location_preference.to_s.humanize %></span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Profile Overview -->
    <div class="grid grid-cols-1 gap-4 mb-6 md:grid-cols-3">
      <div class="md:col-span-2">
        <% if talent_profile.about.present? %>
          <h4 class="mb-2 text-sm font-semibold text-stone-900">ABOUT</h4>
          <p class="mb-4 text-sm text-stone-700"><%= talent_profile.about %></p>
        <% end %>
        
        <% if talent_profile.looking_for.present? %>
          <h4 class="mb-2 text-sm font-semibold text-stone-900">LOOKING FOR</h4>
          <p class="text-sm text-stone-700"><%= talent_profile.looking_for %></p>
        <% end %>
      </div>
      
      <div>
        <div class="p-4 border rounded-lg border-stone-200 bg-stone-50">
          <% if talent_profile.ghostwriter_type.present? && talent_profile.ghostwriter_type.any? %>
            <h4 class="mb-3 text-sm font-semibold text-stone-900">GHOSTWRITER TYPE</h4>
            <div class="flex flex-wrap gap-2">
              <% talent_profile.ghostwriter_type.each do |type| %>
                <span class="inline-flex items-center px-3 py-1 text-xs font-medium text-indigo-800 bg-indigo-100 rounded-md">
                  <%= type %>
                </span>
              <% end %>
            </div>
          <% end %>
          
          <% if talent_profile.social_media_specialty.present? && talent_profile.social_media_specialty.any? %>
            <h4 class="mt-4 mb-2 text-sm font-semibold text-stone-900">SOCIAL MEDIA</h4>
            <div class="flex gap-2">
              <% talent_profile.social_media_specialty.each do |platform| %>
                <% 
                  platform_classes = case platform.downcase
                  when "linkedin"
                    "bg-blue-100 text-blue-700"
                  when "instagram"
                    "bg-pink-100 text-pink-700"
                  when "twitter", "x"
                    "bg-blue-100 text-blue-500"
                  when "threads"
                    "bg-stone-100 text-stone-800"
                  when "facebook"
                    "bg-blue-100 text-blue-700"
                  when "tiktok"
                    "bg-black bg-opacity-10 text-stone-800"
                  else
                    "bg-stone-100 text-stone-800"
                  end

                  platform_icon = case platform.downcase
                  when "linkedin"
                    phosphor_icon("linkedin-logo", class: "h-4 w-4")
                  when "instagram"
                    phosphor_icon("instagram-logo", class: "h-4 w-4")
                  when "twitter", "x"
                    phosphor_icon("twitter-logo", class: "h-4 w-4")
                  when "threads"
                    phosphor_icon("threads-logo", class: "h-4 w-4")
                  when "facebook"
                    phosphor_icon("facebook-logo", class: "h-4 w-4")
                  when "tiktok"
                    phosphor_icon("tiktok-logo", class: "h-4 w-4")
                  else
                    phosphor_icon("globe", class: "h-4 w-4")
                  end
                %>
                <span class="flex items-center justify-center p-1.5 rounded-md <%= platform_classes %>">
                  <%= platform_icon %>
                </span>
              <% end %>
            </div>
          <% end %>
          
          <% if talent_profile.price_range_min.present? || talent_profile.price_range_max.present? %>
            <h4 class="mt-4 mb-2 text-sm font-semibold text-stone-900">PRICING</h4>
            <div class="flex items-center">
              <%= phosphor_icon "currency-dollar", class: "h-4 w-4 mr-1 text-green-600" %>
              <span class="text-sm font-medium">
                <% if talent_profile.price_range_min.present? && talent_profile.price_range_max.present? %>
                  $<%= talent_profile.price_range_min %> - $<%= talent_profile.price_range_max %> USD
                <% elsif talent_profile.price_range_min.present? %>
                  From $<%= talent_profile.price_range_min %> USD
                <% elsif talent_profile.price_range_max.present? %>
                  Up to $<%= talent_profile.price_range_max %> USD
                <% end %>
              </span>
            </div>
            <div class="mt-1 text-xs text-stone-500"><%= talent_profile.pricing_model.to_s.humanize %></div>
          <% end %>
        </div>
      </div>
    </div>
    
    <!-- Portfolio Section -->
    <div class="p-4 mb-5 border rounded-lg border-stone-200 bg-stone-50">
      <h4 class="mb-3 text-sm font-semibold text-stone-900">PORTFOLIO & RESOURCES</h4>
      <div class="grid grid-cols-1 gap-3 md:grid-cols-3">
        <% if talent_profile.portfolio_link.present? %>
          <a href="<%= talent_profile.portfolio_link %>" target="_blank" class="flex items-center gap-2 p-3 transition-colors bg-white border rounded-md border-stone-200 hover:border-indigo-300">
            <%= phosphor_icon "file", class: "h-5 w-5 text-indigo-600" %>
            <span class="text-sm font-medium text-stone-800">View Portfolio</span>
            <%= phosphor_icon "arrow-square-out", class: "h-4 w-4 ml-auto text-stone-400" %>
          </a>
        <% end %>
        
        <% if talent_profile.vsl_link.present? %>
          <a href="<%= talent_profile.vsl_link %>" target="_blank" class="flex items-center gap-2 p-3 transition-colors bg-white border rounded-md border-stone-200 hover:border-indigo-300">
            <%= phosphor_icon "file-text", class: "h-5 w-5 text-indigo-600" %>
            <span class="text-sm font-medium text-stone-800">Watch Video Pitch</span>
            <%= phosphor_icon "arrow-square-out", class: "h-4 w-4 ml-auto text-stone-400" %>
          </a>
        <% end %>
        
        <% if talent_profile.website_url.present? %>
          <a href="<%= talent_profile.website_url %>" target="_blank" class="flex items-center gap-2 p-3 transition-colors bg-white border rounded-md border-stone-200 hover:border-indigo-300">
            <%= phosphor_icon "link", class: "h-5 w-5 text-blue-600" %>
            <span class="text-sm font-medium text-stone-800">Professional Website</span>
            <%= phosphor_icon "arrow-square-out", class: "h-4 w-4 ml-auto text-stone-400" %>
          </a>
        <% end %>
      </div>
    </div>
    
    <!-- Footer with action button -->
    <div class="flex justify-end">
      <% if Current.user.has_pending_chat_request_with?(talent_profile.user) %>
        <button disabled class="flex items-center px-6 py-2.5 text-sm font-medium text-white bg-stone-400 rounded-md cursor-not-allowed">
          Request Sent
          <%= phosphor_icon "check", class: "inline-block h-4 w-4 ml-2" %>
        </button>
      <% else %>
        <button type="button"
                data-action="click->chat-request-button#openModal"
                data-talent-user-id="<%= talent_profile.user.id %>"
                class="flex items-center px-6 py-2.5 text-sm font-medium text-white bg-indigo-600 rounded-md hover:bg-indigo-700">
          Invite to Chat
          <%= phosphor_icon "arrow-right", class: "inline-block h-4 w-4 ml-2" %>
        </button>
      <% end %>
    </div>
  </div>
</div>
