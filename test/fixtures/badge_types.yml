# == Schema Information
#
# Table name: badge_types
#
#  id               :bigint           not null, primary key
#  active           :boolean          default(TRUE)
#  background_color :string           default("#ffffff"), not null
#  description      :text             not null
#  icon             :string           not null
#  name             :string           not null
#  priority         :integer          default(0)
#  text_color       :string           default("#000000"), not null
#  created_at       :datetime         not null
#  updated_at       :datetime         not null
#
# Indexes
#
#  index_badge_types_on_active                (active)
#  index_badge_types_on_active_and_priority   (active,priority)
#  index_badge_types_on_name                  (name) UNIQUE
#  index_badge_types_on_priority              (priority)
#

verified:
  id: <%= ActiveRecord::FixtureSet.identify(:verified) %>
  name: "Verified"
  description: "Indicates a verified ghostwriter with confirmed credentials and identity"
  background_color: "#3b82f6"
  text_color: "#ffffff"
  icon: "check-circle"
  priority: 0
  active: true

ghostwrote_choice:
  id: <%= ActiveRecord::FixtureSet.identify(:ghostwrote_choice) %>
  name: "Ghostwrote Choice"
  description: "Platform-endorsed exceptional talent with proven track record"
  background_color: "#8b5cf6"
  text_color: "#ffffff"
  icon: "star"
  priority: 1
  active: true

premium:
  id: <%= ActiveRecord::FixtureSet.identify(:premium) %>
  name: "Premium"
  description: "Premium tier ghostwriter with advanced skills and experience"
  background_color: "#f59e0b"
  text_color: "#ffffff"
  icon: "crown"
  priority: 2
  active: true

expert:
  id: <%= ActiveRecord::FixtureSet.identify(:expert) %>
  name: "Expert"
  description: "Subject matter expert in specific niches or industries"
  background_color: "#10b981"
  text_color: "#ffffff"
  icon: "graduation-cap"
  priority: 3
  active: true

rising_star:
  id: <%= ActiveRecord::FixtureSet.identify(:rising_star) %>
  name: "Rising Star"
  description: "Promising new talent showing exceptional potential"
  background_color: "#ec4899"
  text_color: "#ffffff"
  icon: "sparkles"
  priority: 4
  active: true

inactive_badge:
  id: <%= ActiveRecord::FixtureSet.identify(:inactive_badge) %>
  name: "Inactive Badge"
  description: "This badge is no longer active and should not be assigned"
  background_color: "#6b7280"
  text_color: "#ffffff"
  icon: "x-circle"
  priority: 99
  active: false
